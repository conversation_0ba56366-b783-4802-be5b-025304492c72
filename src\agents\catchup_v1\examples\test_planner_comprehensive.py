#!/usr/bin/env python3
"""
Comprehensive test for the fixed planner node.

This test verifies that the planner correctly:
1. Handles location data (latitude/longitude) without errors
2. Parses JSON responses from LLM (including markdown-wrapped JSON)
3. Creates valid task plans for complex requests
4. Handles edge cases gracefully
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from agents.catchup_v1.nodes.planner import planner_node
from agents.catchup_v1.state import create_initial_state
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig


async def test_planner_comprehensive():
    """Comprehensive test of the planner functionality."""
    
    print("🧪 Comprehensive Planner Test")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "Restaurant Booking",
            "message": "Find me a restaurant in Milan and book a table for tonight",
            "latitude": "45.4666",
            "longitude": "9.1832",
            "expected_tasks": ["search", "book", "confirm"]
        },
        {
            "name": "Deal Search with Communication",
            "message": "Search for aperitivo deals with 20% discount and send me the results",
            "latitude": "45.4666", 
            "longitude": "9.1832",
            "expected_tasks": ["search", "send"]
        },
        {
            "name": "Multi-step Complex Request",
            "message": "Find hotels with breakfast, book one for next week, and send confirmation via WhatsApp",
            "latitude": "45.4666",
            "longitude": "9.1832", 
            "expected_tasks": ["search", "book", "send"]
        },
        {
            "name": "No Location Data",
            "message": "Find me a good restaurant",
            "latitude": None,
            "longitude": None,
            "expected_tasks": ["search"]
        }
    ]
    
    config = RunnableConfig(
        configurable={
            "model_name": "openai/gpt-4o-mini",
            "model_temperature": 0.1
        }
    )
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['name']}")
        print(f"   Message: '{test_case['message']}'")
        print(f"   Location: {test_case['latitude']}, {test_case['longitude']}")
        
        try:
            # Create state
            state = create_initial_state(
                user_id=f"test_user_{i}",
                session_id=f"test_session_{i}",
                email_address="<EMAIL>",
                latitude=test_case["latitude"],
                longitude=test_case["longitude"]
            )
            
            # Add test message
            state["messages"] = [HumanMessage(content=test_case["message"])]
            
            # Call planner
            result = await planner_node(state, config)
            
            # Analyze results
            success = True
            error_msg = None
            
            if "current_plan" in result and result["current_plan"]:
                plan = result["current_plan"]
                print(f"   ✅ Created plan with {len(plan)} tasks:")
                
                for j, task in enumerate(plan, 1):
                    priority_emoji = {"low": "🔵", "medium": "🟡", "high": "🟠", "critical": "🔴"}.get(task.get("priority", "medium"), "⚪")
                    print(f"      {j}. {priority_emoji} {task['content']}")
                
                # Check conversation phase
                if "conversation_phase" in result:
                    phase = result["conversation_phase"]
                    if phase.get("phase") == "planning" and phase.get("confidence", 0) > 0.8:
                        print(f"   📊 Phase: {phase['phase']} (confidence: {phase['confidence']})")
                    else:
                        success = False
                        error_msg = f"Unexpected phase: {phase}"
                
            else:
                success = False
                error_msg = "No plan created"
                
                # Check if it's a graceful fallback
                if "messages" in result and result["messages"]:
                    msg = result["messages"][0].content
                    if "step by step" in msg.lower() or "assist you directly" in msg.lower():
                        print(f"   ⚠️  Graceful fallback: {msg[:50]}...")
                        success = True  # This is acceptable behavior
                        error_msg = None
            
            results.append({
                "name": test_case["name"],
                "success": success,
                "error": error_msg,
                "plan_size": len(result.get("current_plan", [])),
                "has_fallback": "messages" in result and result["messages"]
            })
            
            if success:
                print(f"   ✅ PASS")
            else:
                print(f"   ❌ FAIL: {error_msg}")
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            results.append({
                "name": test_case["name"],
                "success": False,
                "error": str(e),
                "plan_size": 0,
                "has_fallback": False
            })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results if r["success"])
    total = len(results)
    
    print(f"Overall Success Rate: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! The planner is working correctly.")
    else:
        print("\n❌ Failed tests:")
        for r in results:
            if not r["success"]:
                print(f"  - {r['name']}: {r['error']}")
    
    print(f"\nPlan Statistics:")
    total_tasks = sum(r["plan_size"] for r in results)
    avg_tasks = total_tasks / len(results) if results else 0
    print(f"  - Total tasks created: {total_tasks}")
    print(f"  - Average tasks per plan: {avg_tasks:.1f}")
    print(f"  - Tests with fallback: {sum(1 for r in results if r['has_fallback'])}")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(test_planner_comprehensive())
