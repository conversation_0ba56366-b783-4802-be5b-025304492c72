"""Planning node for CatchUp v1 agent."""

from __future__ import annotations

import json
from typing import Dict, Any, List
from datetime import datetime
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import AIMessage, SystemMessage, HumanMessage

from agents.catchup_v1.state import CatchUpV1State, Task
from agents.catchup_v1.configuration import CatchUpV1Configuration
from catchup_v1.models import Plan
from shared.llm_factory import create_llm


async def planner_node(state: CatchUpV1State, config: RunnableConfig) -> Dict[str, Any]:
    """Planning node that analyzes user requests and creates comprehensive plans.
    
    This node:
    1. Analyzes the user's request to understand intent and complexity
    2. Creates a detailed plan with specific, actionable tasks
    3. Estimates task durations and identifies required tools
    4. Sets up dependencies between tasks
    5. Updates the conversation phase to planning
    """
    
    configuration = CatchUpV1Configuration.from_runnable_config(config)
    messages = state.get("messages", [])
    user_context = state.get("user_context", {})
    session_config = state.get("session_config", {})
    
    if not messages:
        return {"messages": [AIMessage(content="No messages to plan for.")]}
    
    # Get the latest user message
    user_message = None
    for msg in reversed(messages):
        if hasattr(msg, 'type') and msg.type == "human":
            user_message = msg
            break
    
    if not user_message:
        return {"messages": [AIMessage(content="No user message found to create a plan for.")]}
    
    # Format location properly
    location = user_context.get('location')
    if location and isinstance(location, dict):
        lat = location.get('latitude')
        lng = location.get('longitude')
        if lat is not None and lng is not None:
            location_str = f"Latitude: {lat}, Longitude: {lng}"
        else:
            location_str = "Location data incomplete"
    else:
        location_str = "Not provided"

    # Create planning prompt using direct message construction to avoid template issues
    system_message = SystemMessage(content=configuration.planner_prompt)

    human_content = f"""
User Request: {user_message.content}

User Context:
- User ID: {user_context.get('user_id', 'Unknown')}
- Location: {location_str}
- Email: {user_context.get('email_address', 'Not provided')}
- Session: {session_config.get('session_id', 'Unknown')}

Available Tools:
- get_all_categories: Get marketplace categories
- search_deals: Search for deals and offers
- get_user_details_by_id: Get user profile information
- get_business_details: Get business information
- create_booking: Create bookings for deals
- sent_email_to_users: Send emails to users
- whatsapps_sent_tool: Send WhatsApp messages
- get_chat_history: Retrieve conversation history
- enhanced_search_deals: Advanced deal search with filters
- validate_booking_request: Validate booking requirements
- send_progress_update: Send progress updates to user
- send_plan_summary: Send comprehensive plan summary

Create a detailed plan to fulfill this request. Break it down into specific, actionable tasks.
Each task should be atomic and executable independently where possible.

Respond with a JSON object containing a "tasks" array. Each task should have:
- content: Clear description of what needs to be done
- priority: "low", "medium", "high", or "critical"
- estimated_duration: Estimated time in seconds
- tools_required: Array of tool names needed
- dependencies: Array of task indices this task depends on (empty if none)

Example:
{{"tasks": [
    {{"content": "Search for restaurants in user's location", "priority": "high", "estimated_duration": 10, "tools_required": ["enhanced_search_deals"], "dependencies": []}},
    {{"content": "Send search results to user via email", "priority": "medium", "estimated_duration": 5, "tools_required": ["sent_email_to_users"], "dependencies": [0]}}
]}}
"""

    human_message_obj = HumanMessage(content=human_content)
    messages = [system_message, human_message_obj]
    
    # Get LLM and create plan
    llm = create_llm(configuration.model_name, temperature=configuration.model_temperature).with_structured_output(Plan)
    
    try:
        # Generate plan
        response = await llm.ainvoke(messages)



        # Extract JSON from markdown code blocks if present
        content = response.content.strip()
        if content.startswith("```json"):
            # Remove markdown code block markers
            content = content[7:]  # Remove ```json
            if content.endswith("```"):
                content = content[:-3]  # Remove closing ```
            content = content.strip()
        elif content.startswith("```"):
            # Handle generic code blocks
            content = content[3:]  # Remove ```
            if content.endswith("```"):
                content = content[:-3]  # Remove closing ```
            content = content.strip()

        # Parse the JSON response
        plan_data = json.loads(content)
        tasks_data = plan_data.get("tasks", [])
        
        if not tasks_data:
            return {
                "messages": [AIMessage(content="I couldn't create a specific plan for your request. Let me help you directly.")],
                "conversation_phase": {
                    "phase": "understanding",
                    "confidence": 0.5,
                    "context": {"planning_failed": True}
                }
            }
        
        # Convert to Task objects
        current_time = datetime.now().isoformat()
        plan_tasks = []
        
        for i, task_data in enumerate(tasks_data):
            # Convert dependency indices to task IDs (we'll update these after creating all tasks)
            task = Task(
                id=f"task_{i}_{int(datetime.now().timestamp())}",
                content=task_data["content"],
                status="pending",
                priority=task_data.get("priority", "medium"),
                estimated_duration=task_data.get("estimated_duration", 30),
                actual_duration=None,
                dependencies=[],  # Will be updated below
                tools_required=task_data.get("tools_required", []),
                created_at=current_time,
                started_at=None,
                completed_at=None,
                error_message=None
            )
            plan_tasks.append(task)
        
        # Update dependencies with actual task IDs
        for i, task_data in enumerate(tasks_data):
            dependency_indices = task_data.get("dependencies", [])
            dependency_ids = []
            for dep_idx in dependency_indices:
                if 0 <= dep_idx < len(plan_tasks):
                    dependency_ids.append(plan_tasks[dep_idx]["id"])
            plan_tasks[i]["dependencies"] = dependency_ids
        
        # Update progress metrics
        progress_metrics = {
            "total_tasks": len(plan_tasks),
            "completed_tasks": 0,
            "failed_tasks": 0,
            "average_task_duration": None,
            "total_execution_time": None,
            "tools_used": [],
            "communication_sent": []
        }
        
        # Update conversation phase
        conversation_phase = {
            "phase": "planning",
            "confidence": 0.9,
            "context": {
                "plan_created_at": current_time,
                "total_tasks": len(plan_tasks),
                "planning_successful": True
            }
        }
        
        # Create plan summary message
        plan_summary = f"📋 Created comprehensive plan with {len(plan_tasks)} tasks:\n\n"
        for i, task in enumerate(plan_tasks, 1):
            priority_emoji = {"low": "🔵", "medium": "🟡", "high": "🟠", "critical": "🔴"}.get(task["priority"], "⚪")
            estimated_time = f" (~{task['estimated_duration']}s)" if task.get("estimated_duration") else ""
            plan_summary += f"{i}. {priority_emoji} {task['content']}{estimated_time}\n"
        
        plan_summary += f"\n🎯 Ready to execute plan. Starting with the first task..."
        
        return {
            "current_plan": plan_tasks,
            "progress_metrics": progress_metrics,
            "conversation_phase": conversation_phase,
            "messages": [AIMessage(content=plan_summary)]
        }
        
    except json.JSONDecodeError as e:
        return {
            "messages": [AIMessage(content=f"I had trouble creating a structured plan. Let me help you step by step instead.")],
            "conversation_phase": {
                "phase": "understanding",
                "confidence": 0.6,
                "context": {"planning_error": "json_parse_error"}
            }
        }
    
    except Exception as e:

        return {
            "messages": [AIMessage(content=f"I encountered an issue while planning. Let me assist you directly.")],
            "conversation_phase": {
                "phase": "understanding",
                "confidence": 0.5,
                "context": {"planning_error": str(e)}
            },
            "error_context": {
                "node": "planner",
                "error": str(e),
                "user_request": user_message.content
            }
        }
